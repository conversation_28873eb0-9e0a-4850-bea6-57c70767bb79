package com.rt.kt.lib.component.tag.view

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.rt.kt.lib.component.tag.ImageLoaderManager
import com.rt.kt.lib.component.tag.TagBean
import com.rt.kt.lib.component.tag.TagType
import androidx.compose.runtime.getValue
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.platform.LocalDensity
import com.rt.kt.lib.component.tag.ImageLoadingValidator
import com.rt.kt.lib.component.tag.TagUtils

/**
 * @ClassName: ImageTag
 * @Description:图片标签组件
 * 支持网络图片和本地图片，对应原生库的FORM_IMAGE类型标签
 * @Date: 2025/6/4
 */

@Composable
fun ImageTag(
    tagBean: TagBean,
    onClick: ((TagBean) -> Unit)? = null,
    modifier: Modifier = Modifier,
    preCalculatedHeight: Float? = null,
    onImageSizeReady: ((Float, Float) -> Unit)? = null
) {
    require(tagBean.type == TagType.IMAGE) {
        "ImageTag only supports IMAGE type"
    }

    val appearance = tagBean.appearance

    // 点击修饰符
    val clickModifier = if (tagBean.isClickable && onClick != null) {
        Modifier.clickable { onClick(tagBean) }
    } else {
        Modifier
    }

    Row(
        modifier = modifier
            .then(clickModifier)
            .clip(appearance.shape),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
        // 使用状态管理图片加载
        var imageBitmap by remember { mutableStateOf<ImageBitmap?>(null) }
        var isLoading by remember { mutableStateOf(true) }
        var hasError by remember { mutableStateOf(false) }
        // 缓存计算的宽度，避免重复计算
        var cachedWidth by remember { mutableStateOf(0f) }

        val density = LocalDensity.current
        // 使用预计算的高度或计算新的高度  否则使用TagUtils中统一的高度计算逻辑
        val tagHeight = preCalculatedHeight?.dp ?: TagUtils.calculateTagHeight(tagBean)

        // 使用 ImageLoadingValidator 进行验证图片加载，防止列表复用时的显示错乱
        val imageLoader = ImageLoaderManager.getTagImageLoader()
        val loadingState = if (!tagBean.imageUrl.isNullOrBlank()) {
            ImageLoadingValidator.ValidatedImageLoader(
                url = tagBean.imageUrl,
                imageLoader = imageLoader,
                onStateChange = { state ->
                    when (state) {
                        is ImageLoadingValidator.LoadingState.Loading -> {
                            isLoading = true
                            hasError = false
                        }
                        is ImageLoadingValidator.LoadingState.Success -> {
                            imageBitmap = state.painter
                            isLoading = false
                            hasError = false
                            // 图片加载成功后计算并缓存宽度，回调宽高（dp）
                            if (state.painter.width > 0 && state.painter.height > 0) {
                                val scaledWidthDp = TagUtils.calculateImageTagRealWidth(state.painter, density, tagHeight.value)
                                cachedWidth = scaledWidthDp // 缓存计算结果
                                val spacingDP = TagUtils.calculateTagSpacing(tagBean, tagBean.appearance)
                                onImageSizeReady?.invoke(scaledWidthDp + spacingDP, tagHeight.value)
                            }
                        }
                        is ImageLoadingValidator.LoadingState.Error -> {
                            imageBitmap = null
                            isLoading = false
                            hasError = true
                        }
                    }
                }
            )
        } else {
            // 没有URL，直接设置为失败状态
            isLoading = false
            hasError = true
            ImageLoadingValidator.LoadingState.Error(IllegalArgumentException("Empty image URL"))
        }

        // 使用缓存的宽度，避免重复计算
        val finalWidth = if (cachedWidth > 0) cachedWidth else tagHeight.value
        Box(
            modifier = Modifier
                .size(width = finalWidth.dp, height = tagHeight)
                .clip(appearance.shape),
            contentAlignment = Alignment.Center
        ) {
            when {
                isLoading -> {
                    // 加载中状态 - 显示空白
                    Box(modifier = Modifier.fillMaxSize())
                }
                imageBitmap != null -> {
                    // 加载成功 - 显示图片
                    Image(
                        bitmap = imageBitmap!!,
                        contentDescription = tagBean.text.ifBlank { "Tag image" },
                        modifier = Modifier.fillMaxSize(),
                        contentScale = ContentScale.Crop
                    )
                }
                else -> {
                    // 加载失败 - 显示空白
                    Box(modifier = Modifier.fillMaxSize())
                }
            }
        }
    }
}
