package com.rt.kt.lib.component.tag.test

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import com.rt.kt.lib.component.tag.AppTag
import com.rt.kt.lib.component.tag.TagBean
import com.rt.kt.lib.component.tag.TagType

/**
 * 单位转换修复验证组件
 * 
 * 修复方案：正确处理dp到sp的单位转换
 * - 解决Android设备上标签间距偏大的问题
 * - 解决iOS设备上文字被截断的问题
 */
@Composable
fun TagWidthTestScreen() {
    Column(
        modifier = Modifier.padding(16.dp)
    ) {
        Text(
            text = "单位转换修复验证",
            style = MaterialTheme.typography.headlineSmall
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text("测试说明：")
        Text("• Android设备：标签间距应该正常，不再偏大")
        Text("• iOS设备：文字应该完整显示，不再被截断")
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 测试积分标签 - "送积分"
        Text("积分标签测试:")
        val pointsTag = TagBean(
            type = TagType.POINTS, 
            text = "送积分", 
            imageUrl = "points_icon", 
            backgroundColor = Color(0xFF9C27B0), 
            textColor = Color.White
        )
        
        AppTag.showTag(
            tags = listOf(pointsTag),
            text = "测试文字",
            showTagsAtStart = true
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 测试折扣标签 - "折8折家哦"
        Text("折扣标签测试:")
        val discountTag1 = TagBean(
            type = TagType.DISCOUNT, 
            text = "折8折家哦", 
            backgroundColor = Color(0xFFF44336), 
            textColor = Color.White
        )
        
        AppTag.showTag(
            tags = listOf(discountTag1),
            text = "测试文字",
            showTagsAtStart = true
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 测试多标签间距
        Text("多标签间距测试:")
        val multiTags = listOf(
            TagBean(type = TagType.FILL, text = "热销", backgroundColor = Color(0xFFFF9800), textColor = Color.White),
            TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color(0xFF4CAF50), textColor = Color(0xFF4CAF50)),
            TagBean(type = TagType.DISCOUNT, text = "折8折家哦", backgroundColor = Color(0xFFF44336), textColor = Color.White),
            TagBean(type = TagType.POINTS, text = "送积分", imageUrl = "points_icon", backgroundColor = Color(0xFF9C27B0), textColor = Color.White)
        )

        AppTag.showTag(
            tags = multiTags,
            text = "多标签组合测试",
            showTagsAtStart = true,
            onTagClick = { tag ->
                println("标签点击: ${tag.text}(${tag.type})")
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 原始问题复现测试
        Text("原始问题复现测试:")
        val originalTags = listOf(
            TagBean(type = TagType.FILL, text = "热销", backgroundColor = Color(0xFFFF9800), textColor = Color.White, isClickable = true, clickToast = "点击了热销标签"),
            TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color(0xFF4CAF50), textColor = Color(0xFF4CAF50)),
            TagBean(type = TagType.STROKE, text = "包邮", borderColor = Color(0xFF4CAF50), textColor = Color(0xFF4CAF50), isClickable = true, clickToast = "点击了热销标签"),
            TagBean(type = TagType.DISCOUNT, text = "折8折家哦", backgroundColor = Color(0xFFF44336), textColor = Color.White),
            TagBean(type = TagType.DISCOUNT, text = "8折家哦", backgroundColor = Color(0xFFF44336), textColor = Color.White),
            TagBean(type = TagType.POINTS, text = "送积分", imageUrl = "points_icon", backgroundColor = Color(0xFF9C27B0), textColor = Color.White)
        )

        AppTag.showTag(
            tags = originalTags,
            text = "多标签组合商品展示",
            showTagsAtStart = true,
            onTagClick = { tag ->
                println("标签点击: ${tag.text}-${tag.clickToast}(${tag.type})")
            }
        )
    }
}

/**
 * 修复说明
 * 
 * ## 问题根源
 * 单位转换错误导致的两个问题：
 * 
 * ### 1. Android设备上标签间距偏大
 * - `calculateTagSpacing` 返回dp值
 * - 但在Placeholder中直接使用 `spacingValue.sp`
 * - 导致dp值被当作sp值，在Android上造成间距偏大
 * 
 * ### 2. iOS设备上文字被截断
 * - `calculateTagSize` 返回dp值
 * - 但在Placeholder中直接使用 `width.sp` 和 `height.sp`
 * - 导致dp值被当作sp值，在iOS上可能造成容器尺寸不准确
 * 
 * ## 解决方案
 * 正确进行dp到sp的单位转换：
 * ```kotlin
 * // 将dp值正确转换为sp值
 * val widthSp = with(density) { widthDp.dp.toSp() }
 * val heightSp = with(density) { heightDp.dp.toSp() }
 * val spacingValueSp = with(density) { spacingValueDp.dp.toSp() }
 * ```
 * 
 * ## 修复效果
 * - Android设备：标签间距恢复正常，不再偏大
 * - iOS设备：文字完整显示，不再被截断
 * - 跨平台一致性：两个平台显示效果一致
 */
