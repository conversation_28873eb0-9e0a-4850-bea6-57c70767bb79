package com.rt.kt.lib.component.tag

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.rememberTextMeasurer
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp

/**
 * @ClassName: TagUtils
 * @Description:
 * @Date: 2025/6/4
 */
/**
 * 标签工具类
 *
 * 这是KMP Tag Library的核心工具类，提供了标签处理所需的各种工具方法。*
 * 主要功能模块：
 * 1. 颜色处理：颜色解析、转换、对比度计算
 * 2. 文字测量：文字宽度测量、高度计算、缓存管理
 * 3. 布局计算：基线调整、行高校正、尺寸计算
 * 4. 数据处理：标签验证、列表处理、类型转换
 * 5. 样式管理：默认样式设置、样式获取
 *
 * 核心特性：
 * - 高性能的文字测量缓存机制
 * - 安全的颜色解析，支持多种格式
 * - 智能的对比度计算，确保可读性
 * - 完整的标签数据验证
 * - 灵活的样式管理系统
 */
object TagUtils {

    /**
     * 调试模式开关
     * 对应原生版本的isDebug字段
     */
    var isDebugMode: Boolean = false

    /**
     * 特殊颜色标志位
     * 对应原生库的COLOR_NONE，用于标识无颜色状态
     */
    const val COLOR_NONE_VALUE = 1L

    /**
     * 安全解析颜色字符串
     * @param colorString 颜色字符串，支持 #RRGGBB 和 #AARRGGBB 格式
     * @param defaultColor 默认颜色
     * @return 解析后的Color对象
     */
    fun parseColor(colorString: String?, defaultColor: Color = Color.Black): Color {
        if (colorString.isNullOrBlank()) return defaultColor

        return try {
            val cleanColor = colorString.trim()
            when {
                cleanColor.startsWith("#") -> {
                    val hex = cleanColor.substring(1)
                    when (hex.length) {
                        6 -> {
                            // #RRGGBB
                            val rgb = hex.toLong(16)
                            Color(
                                red = ((rgb shr 16) and 0xFF) / 255f,
                                green = ((rgb shr 8) and 0xFF) / 255f,
                                blue = (rgb and 0xFF) / 255f,
                                alpha = 1f
                            )
                        }
                        8 -> {
                            // #AARRGGBB
                            val argb = hex.toLong(16)
                            Color(
                                alpha = ((argb shr 24) and 0xFF) / 255f,
                                red = ((argb shr 16) and 0xFF) / 255f,
                                green = ((argb shr 8) and 0xFF) / 255f,
                                blue = (argb and 0xFF) / 255f
                            )
                        }
                        else -> defaultColor
                    }
                }
                else -> defaultColor
            }
        } catch (e: Exception) {
            defaultColor
        }
    }

    /**
     * 解析渐变背景色终止颜色
     * 对应原生库的getBgEndColor方法
     *
     * 特殊处理逻辑：
     * - 如果颜色值解析结果等于COLOR_NONE，则修改为COLOR_NONE+1避免冲突
     * - 空字符串返回null表示无渐变
     *
     * @param bgColorEnd 背景结束颜色字符串
     * @param defaultColor 默认颜色
     * @return 解析后的Color对象，null表示无渐变
     */
    fun parseEndColor(bgColorEnd: String?, defaultColor: Color = Color.White): Color? {
        if (bgColorEnd.isNullOrBlank()) {
            return null // 无渐变
        }

        val parsedColor = parseColor(bgColorEnd, defaultColor)

        // 使用统一的兼容性处理方法
        return processColorForCompatibility(parsedColor)
    }

    /**
     * 处理Color对象的兼容性问题
     * 检查是否与COLOR_NONE冲突，如果冲突则微调
     *
     * @param color 要处理的颜色
     * @return 处理后的颜色，确保不与COLOR_NONE冲突
     */
    fun processColorForCompatibility(color: Color): Color {
        val colorValue = color.toArgb().toLong() and 0xFFFFFFFFL
        return if (colorValue == COLOR_NONE_VALUE) {
            // 避免与COLOR_NONE冲突，微调颜色值
            color.copy(
                alpha = if (color.alpha == 1f) 0.999f else color.alpha
            )
        } else {
            color
        }
    }

    /**
     * 测量文字宽度（使用Compose缓存）
     * 对应原生库的measureText方法
     *
     * @param text 要测量的文字
     * @param textSize 文字大小（sp单位）
     * @return 文字宽度（dp单位）
     */
    @Composable
    fun measureTextWidth(text: String, textSize: Float): Float {
        if (text.isBlank()) return 0f

        // 使用Compose的remember进行缓存，比手动缓存更高效
        return calculateTextWidth(text, textSize)
    }

    /**
     * 计算文字宽度的精确实现
     * 使用Compose TextMeasurer获取真实的文字宽度
     *
     * @param text 要测量的文字
     * @param textSizeSp 文字大小（sp单位）
     * @return 文字宽度（dp单位，用于统一的布局计算）
     */
    @Composable
    private fun calculateTextWidth(text: String, textSizeSp: Float): Float {
        val textMeasurer = rememberTextMeasurer()
        val density = LocalDensity.current

        val textLayoutResult = remember(text, textSizeSp) {
            textMeasurer.measure(
                text = text,
                style = TextStyle(
                    fontSize = textSizeSp.sp  // ✅ 直接创建sp单位
                )
            )
        }

        //返回dp值，确保单位一致性
        return textLayoutResult.size.width.toFloat() / density.density
    }

    /**
     * 获取文字高度
     * 对应原生库的getTextHeight方法
     * 使用Compose TextMeasurer获取真实的字体度量
     *
     * @param textSizeSp 文字大小（sp单位）
     * @return 文字高度（px单位）
     */
    @Composable
    fun getTextHeight(textSizeSp: Float): Float {
        val textMeasurer = rememberTextMeasurer()

        val textLayoutResult = remember(textSizeSp) {
            textMeasurer.measure(
                text = "Ag", // 使用包含ascent和descent的测试字符
                style = TextStyle(
                    fontSize = textSizeSp.sp
                )
            )
        }

        return textLayoutResult.size.height.toFloat()
    }

    /**
     * 使用TextMeasurer获取真实字体度量
     * 这是最准确的方法，但需要在Composable环境中使用
     *
     * @param text 要测量的文字
     * @param textSizeSp 文字大小（sp单位）
     * @param fontWeight 字体粗细
     * @return 文字布局结果
     */
    @Composable
    fun measureTextMetrics(
        text: String,
        textSizeSp: Float,
        fontWeight: FontWeight? = null
    ): TextLayoutResult {
        val textMeasurer = rememberTextMeasurer()

        return remember(text, textSizeSp, fontWeight) {
            textMeasurer.measure(
                text = text,
                style = TextStyle(
                    fontSize = textSizeSp.sp,
                    fontWeight = fontWeight
                )
            )
        }
    }

    /**
     * 调整基线位置
     * 对应原生库的adjustBaseLine方法
     */
    fun adjustBaseLine(
        originalY: Float,
        containerHeight: Float,
        textHeight: Float
    ): Float {
        val offset1 = containerHeight / 2f
        val offset2 = textHeight / 2f
        return originalY - offset1 + offset2
    }

    /**
     * 检查并校正行高
     * 对应原生库的checkLineFM方法
     */
    fun checkLineHeight(
        currentHeight: Float,
        minHeight: Float
    ): Float {
        return if (currentHeight < minHeight) minHeight else currentHeight
    }

    /**
     * 调整文字大小以匹配标签高度
     * 对应原生库的adjustTextViewSize方法
     *
     * 当标签高度大于文字高度时，自动调整文字大小以保持视觉平衡。
     * 这在固定标签高度的场景中特别有用。
     *
     * @param originalTextSize 原始文字大小（sp）
     * @param tagHeightDp 标签高度（dp）
     * @param density 屏幕密度
     * @return 调整后的文字大小（sp）
     */
    @Composable
    fun adjustTextSize(
        originalTextSize: Float,
        tagHeightDp: Float,
        density: Float = 1f
    ): Float {
        var adjustedTextSize = originalTextSize

        // 最大调整次数，防止无限循环
        var adjustCount = 0
        val maxAdjustCount = 20

        while (needAdjustTextSize(tagHeightDp, adjustedTextSize, density) && adjustCount < maxAdjustCount) {
            adjustedTextSize += 1f
            adjustCount++
        }

        return adjustedTextSize
    }

    /**
     * 判断是否需要调整文字大小
     *
     * @param tagHeightDp 标签高度（dp）
     * @param textSizeSp 文字大小（sp）
     * @param density 屏幕密度
     * @return 是否需要调整
     */
    @Composable
    fun needAdjustTextSize(
        tagHeightDp: Float,
        textSizeSp: Float,
        density: Float = 1f
    ): Boolean {
        if (tagHeightDp <= 0) return false
        // getTextHeight期望sp值，返回px值
        val textHeightPx = getTextHeight(textSizeSp)

        // 计算标签高度（px）
        val tagHeightPx = tagHeightDp * density

        return tagHeightPx > textHeightPx
    }

    /**
     * 获取标签的实际文字大小
     * 缓存计算结果，避免重复计算
     * 完全模拟原生TagAppearance.getTagTextSize逻辑
     */
    @Composable
    fun getTagTextSizeCached(tagBean: TagBean): Float {
        val appearance = tagBean.appearance

        // 使用remember缓存计算结果，避免重复计算
        return remember(tagBean.useFixedHeight, appearance.fixedTextSize, appearance.textSize, appearance.defaultTagTextSizeRate) {
            getTagTextSize(appearance, tagBean.useFixedHeight)
        }
    }

    // ==================== 标签尺寸计算方法 ====================
    /**
     * 计算标签高度 - 统一的高度计算方法
     * 使用真实的字体度量，不再依赖经验值
     *
     * @param tagBean 标签数据
     * @return 标签高度（TextUnit）
     */
    @Composable
    fun calculateTagHeight(tagBean: TagBean, textSize: Float? = null): Dp {
        val appearance = tagBean.appearance
        val density = LocalDensity.current

        return if (appearance.tagHeight.value > 0 && tagBean.useFixedHeight) {
            // 固定高度模式：使用设定的标签高度
            appearance.tagHeight
        } else {
            // 自适应高度模式：基于真实字体度量计算精确高度
            val realTagTextSizeSp = textSize ?: getTagTextSizeCached(tagBean)

            // 使用sp值计算文字高度，getTextHeight返回px值
            val textRealHeightPx = getTextHeight(realTagTextSizeSp)
            val paddingPx = with(density) { appearance.verticalPadding.toPx() }
            val totalHeightPx = textRealHeightPx + paddingPx * 2

            // 转换回sp单位
            with(density) { totalHeightPx.toDp() }
        }
    }
    /**
     * 计算标签宽度
     */
    @Composable
    fun calculateTagWidth(tagBean: TagBean, tagHeight: Float, textSize: Float): Float {
        return when (tagBean.type) {
            TagType.IMAGE -> calculateImageTagWidth(tagBean)
            TagType.POINTS -> calculateJFSpanWidth(tagBean, tagHeight, textSize)
            TagType.DISCOUNT -> calculateDiscountBgTagWidth(tagBean, textSize)
            TagType.FILL_AND_STROKE -> calculateFillAndStrokeSpanWidth(tagBean, textSize)
            else -> calculateFillStrokeSpanWidth(tagBean, textSize)
        }
    }

    /**
     * 计算图片标签宽度 - 预估
     */
    @Composable
    private fun calculateImageTagWidth(tagBean: TagBean): Float {
        val appearance = tagBean.appearance
        var spanWidth = 0F
//        spanWidth += calculateTagSpacing(tagBean, appearance)
        return spanWidth
    }

    @Composable
    private fun calculateTagWidthTemplate(
        tagBean: TagBean,
        textSize: Float,
        contentWidthBlock: @Composable (appearance: TagAppearance, paddingV: Float, paddingH: Float) -> Float
    ): Float {
        val appearance = tagBean.appearance
        val realTagTextSize = textSize//getTagTextSizeCached(tagBean)
        val paddingV = getTagTextPaddingV(appearance, realTagTextSize)
        val paddingH = getTagTextPaddingH(appearance, realTagTextSize)

        var frameWidth = contentWidthBlock(appearance, paddingV, paddingH)

        if (tagBean.isClickable) {
            frameWidth += appearance.arrowWidth.value + appearance.arrowSpacing.value
        }
        var spanWidth = frameWidth
        spanWidth += calculateTagSpacing(tagBean, appearance)
        return spanWidth
    }

    /**
     * 计算积分标签宽度
     */
    @Composable
    private fun calculateJFSpanWidth(tagBean: TagBean, tagHeight: Float, textSize: Float): Float = calculateTagWidthTemplate(tagBean, textSize) { appearance, paddingV, paddingH ->
        val frame1Width = tagHeight // 图标部分宽度 = 标签高度
        val frame2Width = measureTextWidth(tagBean.text, textSize) + 2 * paddingH
        frame1Width + frame2Width
    }

    /**
     * 计算折扣标签宽度
     */
    @Composable
    private fun calculateDiscountBgTagWidth(tagBean: TagBean, textSize: Float): Float = calculateTagWidthTemplate(tagBean, textSize) { appearance, paddingV, paddingH ->
        if (tagBean.text.length == 1) {
            // 单字符：正方形
            dealForSquareFrame(textSize, paddingV, appearance)
        } else {
            //分别计算每个字符
            val frame1Width = measureTextWidth(tagBean.text.take(1), textSize) + 2 * paddingH
            val frame2Width = measureTextWidth(tagBean.text.drop(1), textSize) + 2 * paddingH
            frame1Width + frame2Width
        }
    }

    /**
     * 计算填充+描边标签宽度
     */
    @Composable
    private fun calculateFillAndStrokeSpanWidth(tagBean: TagBean, textSize: Float): Float = calculateTagWidthTemplate(tagBean, textSize) { appearance, paddingV, paddingH ->
        if (tagBean.text.length == 1) {
            dealForSquareFrame(textSize, paddingV, appearance)
        } else {
            measureTextWidth(tagBean.text, textSize) + 2 * paddingH
        }
    }

    /**
     * 计算填充/描边标签宽度
     */
    @Composable
    private fun calculateFillStrokeSpanWidth(tagBean: TagBean, textSize: Float): Float = calculateTagWidthTemplate(tagBean, textSize) { appearance, paddingV, paddingH ->
        if (tagBean.text.length == 1) {
            dealForSquareFrame(textSize, paddingV, appearance)
        } else {
            measureTextWidth(tagBean.text, textSize) + 2 * paddingH
        }
    }

    /**
     * 图片标签 宽度计算方法
     * @param imageBitmap 图片资源，未加载时可为null
     * @param tagHeight 标签高度
     * @return 高度dp
     */
    fun calculateImageTagRealWidth(
        imageBitmap: ImageBitmap?,
        density: Density,
        tagHeight: Float
    ): Float {
        val widthDp = if (imageBitmap != null && imageBitmap.height > 0) {
            val imgWidthDp = with(density) { imageBitmap.width.toDp().value }
            val imgHeightDp = with(density) { imageBitmap.height.toDp().value }
            imgWidthDp * (tagHeight / imgHeightDp)
        } else {
            tagHeight
        }
        return widthDp
    }

    /**
     * 间距处理逻辑
     *
     * @param tagBean 标签数据
     * @param appearance 标签样式
     * @return 间距值（dp单位，避免双重单位转换）
     */
    fun calculateTagSpacing(tagBean: TagBean, appearance: TagAppearance): Float {
        var spacingDp = 0f

        // 多个标签,添加标签间距
        if (!tagBean.isLastTag()) {
            spacingDp += appearance.tagSpacing.value
        }

        // 从开始位置显示,最后一个标签,并且后面有文字内容,添加标签与文字间距
        if (tagBean.fromStart && tagBean.hasText && tagBean.isLastTag()) {
            spacingDp += appearance.textSpacing.value
        }

        // 从结束位置显示,第一个标签,添加便签与文字间的间距
        if (!tagBean.fromStart && tagBean.isFirstTag()) {
            spacingDp += appearance.textSpacing.value
        }

        // 直接返回dp值，避免在calculateTagSize中的双重单位转换
        return spacingDp
    }

    // ==================== 辅助函数  ====================

    private fun getTagTextSize(appearance: TagAppearance, useFixedTagHeight: Boolean): Float {
        return if (useFixedTagHeight && appearance.fixedTextSize.value > 0) {
            appearance.fixedTextSize.value
        } else if (appearance.textSize.value > 0) {
            appearance.textSize.value
        } else {
            // 模拟：rawPaint.getTextSize() * defaultTagTextSizeRate
            12f * appearance.defaultTagTextSizeRate
        }
    }

    private fun getTagTextPaddingV(appearance: TagAppearance, tagTextSize: Float): Float {
        return if (appearance.verticalPadding.value >= 0) {
            appearance.verticalPadding.value
        } else {
            tagTextSize * appearance.defaultPaddingVerticalRate
        }
    }

    private fun getTagTextPaddingH(appearance: TagAppearance, tagTextSize: Float): Float {
        return if (appearance.horizontalPadding.value >= 0) {
            appearance.horizontalPadding.value
        } else {
            tagTextSize * appearance.defaultPaddingHorizontalRate
        }
    }

    @Composable
    private fun dealForSquareFrame(tagTextSize: Float, paddingV: Float, appearance: TagAppearance): Float {
        val textHeight = getTextHeight(tagTextSize)  // 使用真实字体度量

        return if (appearance.cornerRadius.value >= tagTextSize / 2) {
            // 半圆角的情况下调整需要稍微调大边距
            textHeight + 2 * paddingV * appearance.circleFrameScale
        } else {
            textHeight + 2 * paddingV
        }
    }

    // ==================== 数据处理方法 ====================

    /**
     * 创建标签列表的索引信息
     * 为标签列表中的每个标签设置正确的索引和总数信息
     */
    fun processTagList(tags: List<TagBean>): List<TagBean> {
        return tags.mapIndexed { index, tag ->
            tag.copy(
                tagIndex = index,
                tagCount = tags.size
            )
        }
    }

    // ==================== 样式管理方法 ====================

    /**
     * 默认样式存储
     */
    private var defaultAppearance: TagAppearance = TagAppearance.Default
    private var defaultRoundAppearance: TagAppearance = TagAppearance.Round

    /**
     * 设置默认标签样式
     * 对应原生TagUtils.setDefaultAppearance()
     */
    fun setDefaultAppearance(appearance: TagAppearance) {
        defaultAppearance = appearance
    }

    /**
     * 设置默认圆角标签样式
     * 对应原生TagUtils.setDefaultRoundAppearance()
     */
    fun setDefaultRoundAppearance(appearance: TagAppearance) {
        defaultRoundAppearance = appearance
    }

    /**
     * 获取默认样式
     */
    fun getDefaultAppearance(): TagAppearance = defaultAppearance

    /**
     * 获取默认圆角样式
     */
    fun getDefaultRoundAppearance(): TagAppearance = defaultRoundAppearance
}
