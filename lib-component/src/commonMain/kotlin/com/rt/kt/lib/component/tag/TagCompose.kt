package com.rt.kt.lib.component.tag

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier

import androidx.compose.ui.text.TextStyle
import com.rt.kt.lib.component.tag.view.DiscountTag
import com.rt.kt.lib.component.tag.view.FillTag
import com.rt.kt.lib.component.tag.view.ImageTag
import com.rt.kt.lib.component.tag.view.PointsTag
import com.rt.kt.lib.component.tag.view.StrokeTag
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.runtime.mutableStateMapOf
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.background
import androidx.compose.ui.graphics.Color

/**
 * @ClassName: TagCompose
 * @Description:
 * @Date: 2025/6/4
 */
/**
 * 标签组合组件 - 主要入口点
 *
 * 基于InlineTextContent模拟SpannableString效果
 * 图片加载完全通过ImageLoaderManager中的图片加载器处理
 *
 * @param tags 标签列表
 * @param text 文字内容
 * @param textStyle 文字样式
 * @param showTagsAtStart 标签是否在前面（true: 标签在前，false: 文字在前）
 * @param onTagClick 标签点击回调
 * @param forceTagHeight 是否强制标签高度
 * @param maxLines 最大行数
 * @param overflow 文字溢出处理方式
 * @param modifier 修饰符
 */
@Composable
fun TagGroup(
    tags: List<TagBean>,
    text: String = "",
    textStyle: TextStyle = MaterialTheme.typography.bodyMedium,
    showTagsAtStart: Boolean = true,
    onTagClick: ((TagBean) -> Unit)? = null,
    forceTagHeight: Boolean = false,
    maxLines: Int = Int.MAX_VALUE,
    overflow: TextOverflow = TextOverflow.Clip,
    modifier: Modifier = Modifier
) {
    // 如果没有内容，不渲染任何东西
    if (tags.isEmpty() && text.isBlank()) return

    // 如果只有文字，直接显示文字
    if (tags.isEmpty()) {
        BasicText(
            text = text,
            style = textStyle,
            maxLines = maxLines,
            overflow = overflow,
            modifier = modifier
        )
        return
    }

    // 预处理标签数据（合并重复逻辑）
    val processedTags = remember(tags) {
        TagUtils.processTagList(tags)
    }

    // 处理标签高度逻辑
    val localDensity = LocalDensity.current
    val density = localDensity.density
    val (adjustedTextStyle, adjustedTags) = processTagHeightLogic(processedTags, textStyle, forceTagHeight, density)

    // 使用原生风格的混合文本布局
    MixedStyleText(
        tags = adjustedTags,
        text = text,
        showTagsAtStart = showTagsAtStart,
        maxLines = maxLines,
        overflow = overflow,
        textStyle = adjustedTextStyle,
        onTagClick = onTagClick,
        modifier = modifier
    )
}

/**
 * 原生风格的混合文本组件

 * 1. 构建完整的文本内容（标签占位符 + 文字）
 * 2. 使用BasicText组件的自动换行能力（类似TextView）
 * 3. 通过InlineContent在占位符位置插入标签组件
 */
@Composable
private fun MixedStyleText(
    tags: List<TagBean>,
    text: String,
    showTagsAtStart: Boolean,
    maxLines: Int,
    overflow: TextOverflow,
    textStyle: TextStyle,
    onTagClick: ((TagBean) -> Unit)?,
    modifier: Modifier
) {
    // 图片标签宽高状态Map
    val imageTagSizeMap = remember { mutableStateMapOf<String, Pair<Float, Float>>() }

    // 构建混合内容 - tags参数已经是经过processTagHeightLogic处理的adjustedTags
    val (annotatedText, inlineContentMap) = remember(tags, text, showTagsAtStart) {
        buildNativeStyleContent(tags, text, showTagsAtStart)
    }

    // 创建内联内容映射 - 优化计算逻辑，支持间距标签
    // 合并标签内容映射和间距占位符处理
    val allInlineContent = mutableMapOf<String, InlineTextContent>()

    // 处理标签内容
    inlineContentMap.forEach { (placeholderId, tagBean) ->
        val textSize = TagUtils.getTagTextSizeCached(tagBean)
        // 使用统一的尺寸计算方法
        val (width, height) = if (tagBean.type == TagType.IMAGE) {
            imageTagSizeMap[tagBean.getUniqueId()] ?: calculateTagSize(tagBean, textSize)
        } else {
            calculateTagSize(tagBean, textSize)
        }

        allInlineContent[placeholderId] = InlineTextContent(
            placeholder = Placeholder(
                width = width.sp,
                height = height.sp,
                placeholderVerticalAlign = PlaceholderVerticalAlign.Center
            )
        ) {
            if (tagBean.type == TagType.IMAGE) {
                ImageTag(
                    tagBean = tagBean,
                    onClick = onTagClick,
                    preCalculatedHeight = height,
                    onImageSizeReady = { widthDp, heightDp ->
                        val key = tagBean.getUniqueId()
                        val old = imageTagSizeMap[key]
                        if (old == null || old.first != widthDp || old.second != heightDp) {
                            imageTagSizeMap[key] = widthDp to heightDp
                        }
                    }
                )
            } else {
                SingleTag(
                    tagBean = tagBean,
                    onTagClick = onTagClick,
                    preCalculatedHeight = height, // 传递预计算的高度
                    preCalculatedTextSize = textSize // 传递预计算的文字大小
                )
            }
        }
    }

    // 处理间距占位符 - 使用TagUtils.calculateTagSpacing获取完整的间距逻辑
    // 为每个可能的间距占位符创建内容
    tags.forEachIndexed { index, tag ->
        // 标签间距占位符 - 只处理在buildNativeStyleContent中实际添加的占位符
        val spacingPlaceholderId = "spacing_$index"

        // 只有非最后一个标签才会在buildNativeStyleContent中添加spacing占位符
        if (!tag.isLastTag()) {
            // 对于标签间距，我们只需要tagSpacing的值
            val spacingValue = tag.appearance.tagSpacing.value

            // 只有当间距值大于0时才创建间距占位符
            if (spacingValue > 0) {
                allInlineContent[spacingPlaceholderId] = InlineTextContent(
                    placeholder = Placeholder(
                        width = spacingValue.sp,
                        height = 1.sp, // 最小高度，避免影响行高
                        placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                    )
                ) {
                    // 渲染透明的间距组件
                    SpacingComponent(spacingValue)
                }
            }
        }
    }

    // 标签与文字间距占位符
    // 处理在buildNativeStyleContent中添加的text_spacing占位符
    if (tags.isNotEmpty()) {
        // text_spacing占位符在两种情况下会被添加：
        // 1. 标签在前，最后一个标签后面有文字
        // 2. 标签在后，第一个标签前面有文字
        val textSpacingValue = tags.first().appearance.textSpacing.value
        if (textSpacingValue > 0) {
            allInlineContent["text_spacing"] = InlineTextContent(
                placeholder = Placeholder(
                    width = textSpacingValue.sp,
                    height = 1.sp, // 最小高度，避免影响行高
                    placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                )
            ) {
                // 渲染透明的间距组件
                SpacingComponent(textSpacingValue)
            }
        }
    }

    // 使用BasicText组件的自动换行能力（类似TextView）
    BasicText(
        text = annotatedText,
        style = textStyle,
        maxLines = maxLines,
        overflow = overflow,
        inlineContent = allInlineContent,
        modifier = modifier
    )
}

/**
 * 单个标签组件
 * 图片通过ImageLoaderManager处理
 */
@Composable
private fun SingleTag(
    tagBean: TagBean,
    onTagClick: ((TagBean) -> Unit)?,
    preCalculatedHeight: Float? = null, // 预计算的高度（dp值）
    preCalculatedTextSize: Float? = null // 预计算的文字大小（sp值）
) {
    when (tagBean.type) {
        TagType.FILL, TagType.FILL_AND_STROKE -> {
            FillTag(
                tagBean = tagBean,
                onClick = onTagClick,
                preCalculatedHeight = preCalculatedHeight,
                preCalculatedTextSize = preCalculatedTextSize
            )
        }
        TagType.STROKE -> {
            StrokeTag(
                tagBean = tagBean,
                onClick = onTagClick,
                preCalculatedHeight = preCalculatedHeight,
                preCalculatedTextSize = preCalculatedTextSize
            )
        }
        TagType.IMAGE -> {
            // 图片通过ImageLoaderManager处理
            ImageTag(
                tagBean = tagBean,
                onClick = onTagClick
            )
        }
        TagType.DISCOUNT -> {
            DiscountTag(
                tagBean = tagBean,
                onClick = onTagClick,
                preCalculatedHeight = preCalculatedHeight,
                preCalculatedTextSize = preCalculatedTextSize
            )
        }
        TagType.POINTS -> {
            PointsTag(
                tagBean = tagBean,
                onClick = onTagClick,
                preCalculatedHeight = preCalculatedHeight,
                preCalculatedTextSize = preCalculatedTextSize
            )
        }
    }
}

/**
 * 统一计算标签尺寸和文字大小
 *
 * @param tagBean 标签数据
 * @return Triple<宽度, 高度, 文字大小> (单位: Float dp, Float dp, Float sp)
 */
@Composable
private fun calculateTagSize(tagBean: TagBean, textSize: Float): Pair<Float, Float> {
    // 高度计算 - TagUtils现在直接返回dp值
    val heightDp = TagUtils.calculateTagHeight(tagBean, textSize).value

    // 宽度计算 - TagUtils返回的是dp值（文字宽度转换为dp + 间距dp）
    val widthDp = TagUtils.calculateTagWidth(tagBean, heightDp, textSize)

    return Pair(widthDp, heightDp)
}

/**
 * 处理标签高度逻辑
 * 1. 外部文字高度大于设置的固定标签高度 → 显示标签的固定高度
 * 2. 外部文字高度小于设置的固定标签高度 → 标签高度随文字高度自适应，标签文字自适应外部文字大小
 * 3. 若强制设置标签高度 → 调整外部文字大小兼容处理
 */
@Composable
private fun processTagHeightLogic(
    tags: List<TagBean>,
    textStyle: TextStyle,
    forceTagHeight: Boolean,
    density: Float
): Pair<TextStyle, List<TagBean>> {
    if (tags.isEmpty()) return textStyle to tags

    val appearance = tags.first().appearance
    val tagHeightDp = appearance.tagHeight.value

    if (tagHeightDp <= 0)
        return textStyle to tags

    val currentTextSizeSp = textStyle.fontSize.value
    val needsAdjustment = TagUtils.needAdjustTextSize(tagHeightDp, currentTextSizeSp, density)

    return if (forceTagHeight && needsAdjustment) {
        // 规则3：强制标签高度，调整文字大小
        val adjustedTextSize = TagUtils.adjustTextSize(currentTextSizeSp, tagHeightDp, density)
        val adjustedTextStyle = textStyle.copy(fontSize = adjustedTextSize.sp)
        val adjustedTags = tags.map { it.copy(useFixedHeight = true) }
        adjustedTextStyle to adjustedTags
    } else {
        // 规则1&2：根据文字和标签高度关系决定是否使用固定高度
        // 当needsAdjustment为false时（文字高度>=标签高度），使用固定高度
        // 当needsAdjustment为true时（文字高度<标签高度），使用自适应高度
        val adjustedTags = if (!needsAdjustment) {
            // 🎯规则1：外部文字高度大于等于设置的固定标签高度 → 显示标签的固定高度
            tags.map { it.copy(useFixedHeight = true) }
        } else {
            // 🎯 规则2：外部文字高度小于设置的固定标签高度 → 标签高度随文字高度自适应，标签文字自适应
            tags.map { tag ->
                tag.copy(
                    useFixedHeight = false,  // 标签高度自适应外部文字高度
                    // 标签文字大小自适应外部文字大小
                    appearance = tag.appearance.copy(
                        textSize = currentTextSizeSp.sp,  // 使用外部文字大小
                        fixedTextSize = 0.sp,  // 清除固定文字大小设置，确保使用textSize
                        tagHeight = (-1).dp, //清除固定标签高度设置，确保自适应
                    )
                )
            }
        }
        textStyle to adjustedTags
    }
}

/**
 * 构建原生风格的混合内容
 *
 * 1. 根据showTagsAtStart决定标签和文字的位置
 * 2. 为每个标签创建占位符
 * 3. 构建完整的AnnotatedString
 * 4. 通过精确的InlineTextContent占位符实现间距控制
 *
 * ## 精确间距控制方案
 * 新的间距实现方案解决了iOS平台兼容性问题：
 *
 * ### 间距计算逻辑
 * - 使用TagUtils.calculateTagSpacing()方法获取完整的间距逻辑
 * - 包含标签间距、标签与文字间距的复杂判断
 * - 自动处理不同位置标签的间距需求
 *
 * ### 间距占位符
 * - "spacing_N"占位符：每个标签后的间距，由calculateTagSpacing计算
 * - "text_spacing"占位符：文字与标签间距（标签在后的情况）
 * - 透明的SpacingComponent组件渲染实际间距
 *
 * ### 优势
 * - 完整逻辑：使用与原生Android版本相同的间距计算逻辑
 * - 精确控制：间距大小完全由TagAppearance配置和位置逻辑决定
 * - 平台一致性：在iOS和Android上显示效果完全一致
 * - 智能判断：自动处理标签位置、是否有文字等复杂情况
 * - 无重复计算：避免了在标签宽度中包含间距导致的重复计算
 */
private fun buildNativeStyleContent(
    tags: List<TagBean>,
    text: String,
    showTagsAtStart: Boolean
): Pair<AnnotatedString, Map<String, TagBean>> {
    val builder = AnnotatedString.Builder()
    val inlineContentMap = mutableMapOf<String, TagBean>()

    val hasText = text.isNotBlank()

    // 创建带有位置信息的标签副本，用于间距计算
    val tagsWithPosition = tags.mapIndexed { index, tag ->
        tag.copyWithIndex(index, tags.size)
    }

    // 添加标签的辅助函数
    fun addTags() {
        tagsWithPosition.forEachIndexed { index, tag ->
            val placeholderId = "tag_$index"
            builder.appendInlineContent(placeholderId, "${tag.type}-${tag.text}-${tag.imageUrl}")
            inlineContentMap[placeholderId] = tag

             //在标签之间添加精确的间距，根据appearance.tagSpacing.value设置
            if (!tag.isLastTag()) {
                val spacingPlaceholderId = "spacing_$index"
                // 创建一个透明的间距占位符，使用空字符串作为替代文字
                builder.appendInlineContent(spacingPlaceholderId, "")
            }
        }
    }

    if (showTagsAtStart) {
        // 标签在前：标签 → 文字
        addTags()
        if (hasText) {
            // 在标签和文字之间添加精确间距，根据appearance.textSpacing.value设置
            if (tagsWithPosition.isNotEmpty()) {
                val textSpacingId = "text_spacing"
                builder.appendInlineContent(textSpacingId, "")
            }
            builder.append(text)
        }
    } else {
        // 标签在后：文字 → 标签
        if (hasText) {
            builder.append(text)
            // 在文字和标签之间添加精确间距，根据appearance.textSpacing.value设置
            if (tagsWithPosition.isNotEmpty()) {
                val textSpacingId = "text_spacing"
                builder.appendInlineContent(textSpacingId, "")
            }
        }
        addTags()
    }

    return builder.toAnnotatedString() to inlineContentMap
}

/**
 * 间距组件
 *
 * 用于在标签之间或标签与文字之间插入精确的间距。
 * 这个组件完全透明，只占用指定的宽度空间，不影响视觉效果。
 *
 * 使用场景：
 * - 标签间距：根据 appearance.tagSpacing.value 设置
 * - 标签与文字间距：根据 appearance.textSpacing.value 设置
 *
 * @param spacingDp 间距大小（dp值），直接来自TagAppearance配置
 */
@Composable
private fun SpacingComponent(spacingDp: Float) {
    // 创建一个透明的间距组件，宽度为指定的间距值
    Box(
        modifier = Modifier
            .width(spacingDp.dp)
            .height(1.dp) // 最小高度，不影响行高
            .background(Color.Transparent) // 完全透明，不可见
    )
}

