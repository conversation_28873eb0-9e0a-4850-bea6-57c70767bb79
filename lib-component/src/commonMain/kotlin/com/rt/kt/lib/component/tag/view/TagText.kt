package com.rt.kt.lib.component.tag.view

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.text.BasicText
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.sp

/**
 * @ClassName: TagText
 * @Description:标签专用文字组件
 * 解决Compose Text组件的默认行高间距问题，确保文字在标签中紧凑显示
 * @Date: 2025/6/18
 */

@Composable
fun TagText(
    text: String,
    fontSize: TextUnit,
    color: Color,
    fontWeight: FontWeight? = null,
    modifier: Modifier = Modifier,
    textAlign: TextAlign = TextAlign.Left,
    maxLines: Int = 1
) {
    BasicText(
        text = text,
        style = TextStyle(
            fontSize = fontSize,
            fontWeight = fontWeight,
            textAlign = textAlign,
            color = color,
            lineHeight = TextUnit.Unspecified
//            platformStyle = PlatformTextStyle(
//                includeFontPadding = false
//            )
        ),
        maxLines = maxLines,
        modifier = modifier.background(Color.Green).height(IntrinsicSize.Min) ,
    )
}

/**
 * 标签文字组件的使用说明
 *
 * ## 问题背景
 * Compose的Text组件默认会添加上下间距，这在标签中会导致：
 * 1. 标签看起来比预期更高
 * 2. 文字在标签中不够居中
 * 3. 与Android原生TextView的效果不一致
 *
 * ## 解决方案
 * 1. **lineHeight = fontSize**: 设置行高等于字体大小，消除额外的行间距
 * 2. **includeFontPadding = false**: Android平台移除字体内边距
 * 3. **platformStyle**: 平台特定的文字样式优化
 */
